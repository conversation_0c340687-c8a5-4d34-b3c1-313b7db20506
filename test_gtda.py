# Test script to verify global giotto-tda installation
import sys
print("Python path:", sys.path)
print("\nTrying to import giotto-tda libraries...\n")

try:
    # Import giotto-tda components
    from gtda.images import ImageToPointCloud
    from gtda.homology import VietorisRipsPersistence, CubicalPersistence
    from gtda.diagrams import PersistenceEntropy, Amplitude, BettiCurve, NumberOfPoints
    
    print("✅ Successfully imported gtda modules!")
    print("Module paths:")
    print(f"gtda: {sys.modules['gtda'].__file__}")
    print(f"gtda.images: {sys.modules['gtda.images'].__file__}")
    print(f"gtda.homology: {sys.modules['gtda.homology'].__file__}")
    print(f"gtda.diagrams: {sys.modules['gtda.diagrams'].__file__}")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    
except Exception as e:
    print(f"❌ Error: {e}")