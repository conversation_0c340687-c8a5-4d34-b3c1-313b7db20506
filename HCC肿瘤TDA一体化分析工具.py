# -*- coding: utf-8 -*-
"""
HCC肿瘤3D图像TDA一体化分析工具

集成所有功能的单一文件：
1. 批量加载image和mask文件
2. 提取肿瘤区域的拓扑特征
3. 保存到Excel文件
4. 生成可视化图表
5. 生成分析报告

直接运行即可，无需命令行参数
"""
# pip install giotto-tda
import os
import sys
# 从Python路径中移除giotto-tda-master，以确保使用全局安装的giotto-tda
if "/root/giotto-tda-master" in sys.path:
    sys.path.remove("/root/giotto-tda-master")

import glob
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import nibabel as nib
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入必要的库
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from scipy.ndimage import zoom

# 导入giotto-tda库
from gtda.images import ImageToPointCloud
from gtda.homology import VietorisRipsPersistence, CubicalPersistence
from gtda.diagrams import (PersistenceEntropy, Amplitude, BettiCurve, NumberOfPoints,
                          PersistenceLandscape, HeatKernel, Silhouette,
                          PersistenceImage, ComplexPolynomial,
                          ForgetDimension, Scaler, Filtering, PairwiseDistance)
from gtda.plotting import plot_diagram, plot_point_cloud, plot_betti_curves, plot_heatmap

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# ==================== 配置参数 ====================
# 数据路径配置
IMAGE_DIR = "/root/giotto-tda-master/HCCdata/image/ap"
MASK_DIR = "/root/giotto-tda-master/HCCdata/mask/ap"
OUTPUT_DIR = "/root/giotto-tda-master/HCCdata/HCC_TDA_Results"  # 输出目录

# 分析参数配置
MAX_EDGE_LENGTH = 5.0      # Vietoris-Rips复形的最大边长
MAX_POINTS = 8000          # 最大点云大小
MASK_THRESHOLD = 0.5       # Mask二值化阈值
N_WORKERS =   4           # 并行进程数（设为1使用单进程）

# 输出配置
SAVE_PLOTS = True          # 是否保存可视化图片
GENERATE_REPORT = True     # 是否生成分析报告

print("开始导入库...")
print("HCC肿瘤3D图像TDA一体化分析工具")
print("=" * 60)
print(f"图像路径: {IMAGE_DIR}")
print(f"Mask路径: {MASK_DIR}")
print(f"输出目录: {OUTPUT_DIR}")
print("=" * 60)
print("导入完成，开始分析...")


# ==================== 核心分析函数 ====================

def load_nii_image(file_path):
    """加载nii.gz格式的3D医学图像"""
    try:
        img = nib.load(file_path)
        image_data = img.get_fdata()

        if image_data.ndim > 3:
            image_data = image_data[:, :, :, 0]

        return image_data
    except Exception as e:
        print(f"加载图像失败 {os.path.basename(file_path)}: {e}")
        return None


def load_image_and_mask(image_path, mask_path):
    """加载图像和对应的mask文件"""
    try:
        # 加载图像
        image_data = load_nii_image(image_path)
        if image_data is None:
            return None, None

        # 加载mask
        mask_data = load_nii_image(mask_path)
        if mask_data is None:
            return None, None

        # 确保图像和mask尺寸一致
        if image_data.shape != mask_data.shape:
            zoom_factors = [img_dim / mask_dim for img_dim, mask_dim in zip(image_data.shape, mask_data.shape)]
            mask_data = zoom(mask_data, zoom_factors, order=0)

        return image_data, mask_data
    except Exception as e:
        print(f"加载图像和mask失败: {e}")
        return None, None


def extract_tumor_region(image_data, mask_data, mask_threshold=0.5):
    """根据mask提取肿瘤区域"""
    try:
        # 二值化mask
        tumor_mask = mask_data > mask_threshold

        # 检查肿瘤区域是否为空
        if np.sum(tumor_mask) == 0:
            return None, None

        return tumor_mask.astype(np.float32), tumor_mask
    except Exception as e:
        print(f"提取肿瘤区域失败: {e}")
        return None, None


def image_to_point_cloud(binary_image, max_points=10000):
    """将二值化的3D图像转换为点云数据"""
    try:
        image_to_point_cloud = ImageToPointCloud()
        binary_image_batch = binary_image.reshape(1, *binary_image.shape)

        image_to_point_cloud.fit(binary_image_batch)
        point_cloud = image_to_point_cloud.transform(binary_image_batch)[0]

        if len(point_cloud) > max_points:
            indices = np.random.choice(len(point_cloud), max_points, replace=False)
            point_cloud = point_cloud[indices]

        return point_cloud
    except Exception as e:
        print(f"点云转换失败: {e}")
        return None


def compute_persistent_homology_point_cloud(point_cloud, max_edge_length=5.0, n_jobs=N_WORKERS):
    """计算点云数据的持续同调"""
    try:
        persistence = VietorisRipsPersistence(
            metric="euclidean",
            homology_dimensions=(0, 1, 2),
            max_edge_length=max_edge_length,
            n_jobs=n_jobs
        )

        point_cloud_batch = point_cloud.reshape(1, *point_cloud.shape)
        persistence.fit(point_cloud_batch)
        diagrams = persistence.transform(point_cloud_batch)

        return diagrams[0]
    except MemoryError:
        print(f"内存不足: 点云数据过大，请减小MAX_POINTS或MAX_EDGE_LENGTH参数")
        return None
    except Exception as e:
        print(f"点云持续同调计算失败: {e}")
        return None


def compute_persistent_homology_cubical(binary_image, n_jobs=N_WORKERS):
    """计算3D二值图像的立方持续同调"""
    try:
        persistence = CubicalPersistence(
            homology_dimensions=(0, 1, 2),
            n_jobs=n_jobs
        )

        binary_image_batch = binary_image.reshape(1, *binary_image.shape)
        persistence.fit(binary_image_batch)
        diagrams = persistence.transform(binary_image_batch)

        return diagrams[0]
    except MemoryError:
        print(f"内存不足: 图像数据过大，请考虑降采样或减小图像尺寸")
        return None
    except Exception as e:
        print(f"立方持续同调计算失败: {e}")
        return None


def compute_topological_features(diagrams):
    """从持续同调图中提取拓扑特征"""
    if diagrams is None:
        return None

    try:
        features = {}
        diagrams_batch = diagrams.reshape(1, *diagrams.shape)

        # 计算持续熵
        try:
            entropy = PersistenceEntropy(n_jobs=N_WORKERS)
            entropy.fit(diagrams_batch)
            entropy_features = entropy.transform(diagrams_batch)
            features["entropy"] = entropy_features[0]
        except Exception as e:
            print(f"计算持续熵失败: {e}")
            features["entropy"] = np.array([0, 0, 0])

        # 计算多种振幅指标
        amplitude_metrics = ["wasserstein", "bottleneck", "landscape", "betti"]
        for metric in amplitude_metrics:
            try:
                amplitude = Amplitude(metric=metric, n_jobs=N_WORKERS)
                amplitude.fit(diagrams_batch)
                amplitude_features = amplitude.transform(diagrams_batch)
                features[f"amplitude_{metric}"] = amplitude_features[0]
            except Exception as e:
                print(f"计算{metric}振幅失败: {e}")
                features[f"amplitude_{metric}"] = np.array([0, 0, 0])

        # 计算Betti曲线
        try:
            betti = BettiCurve(n_bins=100, n_jobs=N_WORKERS)
            betti.fit(diagrams_batch)
            betti_features = betti.transform(diagrams_batch)
            features["betti"] = betti_features[0]
        except Exception as e:
            print(f"计算Betti曲线失败: {e}")
            features["betti"] = np.zeros((3, 100))

        # 计算持续景观
        try:
            landscape = PersistenceLandscape(n_layers=3, n_bins=50, n_jobs=N_WORKERS)
            landscape.fit(diagrams_batch)
            landscape_features = landscape.transform(diagrams_batch)
            features["landscape"] = landscape_features[0]
        except Exception as e:
            print(f"计算持续景观失败: {e}")
            features["landscape"] = np.zeros((9, 50))  # 3 hom_dims * 3 layers

        # 计算热核表示
        try:
            heat = HeatKernel(sigma=0.1, n_bins=20, n_jobs=N_WORKERS)
            heat.fit(diagrams_batch)
            heat_features = heat.transform(diagrams_batch)
            features["heat"] = heat_features[0]
        except Exception as e:
            print(f"计算热核表示失败: {e}")
            features["heat"] = np.zeros((3, 20, 20))

        # 计算轮廓表示
        try:
            silhouette = Silhouette(power=1.0, n_bins=50, n_jobs=N_WORKERS)
            silhouette.fit(diagrams_batch)
            silhouette_features = silhouette.transform(diagrams_batch)
            features["silhouette"] = silhouette_features[0]
        except Exception as e:
            print(f"计算轮廓表示失败: {e}")
            features["silhouette"] = np.zeros((3, 50))

        # 计算持续图像
        try:
            pers_image = PersistenceImage(sigma=0.1, n_bins=20, n_jobs=N_WORKERS)
            pers_image.fit(diagrams_batch)
            image_features = pers_image.transform(diagrams_batch)
            features["persistence_image"] = image_features[0]
        except Exception as e:
            print(f"计算持续图像失败: {e}")
            features["persistence_image"] = np.zeros((3, 20, 20))

        # 计算复多项式系数
        try:
            complex_poly = ComplexPolynomial(n_coefficients=5, polynomial_type='R', n_jobs=N_WORKERS)
            complex_poly.fit(diagrams_batch)
            poly_features = complex_poly.transform(diagrams_batch)
            features["complex_polynomial"] = poly_features[0]
        except Exception as e:
            print(f"计算复多项式失败: {e}")
            features["complex_polynomial"] = np.zeros(30)  # 3 hom_dims * 5 coeffs * 2 (real+imag)

        # 计算点数
        try:
            n_points = NumberOfPoints(n_jobs=N_WORKERS)
            n_points.fit(diagrams_batch)
            n_points_features = n_points.transform(diagrams_batch)
            features["n_points"] = n_points_features[0]
        except Exception as e:
            print(f"计算点数失败: {e}")
            features["n_points"] = np.array([0, 0, 0])

        # 计算预处理后的特征
        try:
            # 缩放持续图
            scaler = Scaler(metric='landscape', n_jobs=N_WORKERS)
            scaler.fit(diagrams_batch)
            scaled_diagrams = scaler.transform(diagrams_batch)

            # 计算缩放因子作为特征
            features["scale_factor"] = scaler.scale_

            # 过滤小的持续特征
            filtering = Filtering(epsilon=0.01, n_jobs=N_WORKERS)
            filtering.fit(diagrams_batch)
            filtered_diagrams = filtering.transform(diagrams_batch)

            # 计算过滤后的点数
            n_points_filtered = NumberOfPoints(n_jobs=N_WORKERS)
            n_points_filtered.fit(filtered_diagrams)
            filtered_n_points = n_points_filtered.transform(filtered_diagrams)
            features["n_points_filtered"] = filtered_n_points[0]

        except Exception as e:
            print(f"预处理特征计算失败: {e}")
            features["scale_factor"] = np.array([1.0, 1.0, 1.0])
            features["n_points_filtered"] = np.array([0, 0, 0])

        # 计算距离度量特征
        try:
            # 创建参考空图（对角线图）
            empty_diagram = np.zeros((1, 1, 3))
            empty_batch = empty_diagram.reshape(1, *empty_diagram.shape)

            # 计算与空图的距离
            distance_metrics = ['bottleneck', 'wasserstein', 'landscape']
            for metric in distance_metrics:
                try:
                    pairwise_dist = PairwiseDistance(metric=metric, n_jobs=N_WORKERS)
                    pairwise_dist.fit(diagrams_batch)
                    # 计算与自身的距离矩阵的统计量
                    dist_matrix = pairwise_dist.transform(diagrams_batch)
                    features[f"self_distance_{metric}_mean"] = np.mean(dist_matrix)
                    features[f"self_distance_{metric}_std"] = np.std(dist_matrix)
                except Exception as e:
                    print(f"计算{metric}距离失败: {e}")
                    features[f"self_distance_{metric}_mean"] = 0.0
                    features[f"self_distance_{metric}_std"] = 0.0

        except Exception as e:
            print(f"距离度量计算失败: {e}")
            for metric in ['bottleneck', 'wasserstein', 'landscape']:
                features[f"self_distance_{metric}_mean"] = 0.0
                features[f"self_distance_{metric}_std"] = 0.0

        return features
    except Exception as e:
        print(f"拓扑特征提取失败: {e}")
        return None


def extract_summary_features(features_point_cloud, features_cubical, image_info):
    """提取汇总的拓扑特征"""
    summary = {}
    summary.update(image_info)

    # 点云方法特征
    if features_point_cloud is not None:
        # 持续熵
        for i, entropy in enumerate(features_point_cloud["entropy"]):
            summary[f"PC_Entropy_H{i}"] = entropy

        # 多种振幅指标
        amplitude_metrics = ["wasserstein", "bottleneck", "landscape", "betti"]
        for metric in amplitude_metrics:
            if f"amplitude_{metric}" in features_point_cloud:
                for i, amp in enumerate(features_point_cloud[f"amplitude_{metric}"]):
                    summary[f"PC_Amplitude_{metric}_H{i}"] = amp

        # 点数
        for i, n_pts in enumerate(features_point_cloud["n_points"]):
            summary[f"PC_NPoints_H{i}"] = n_pts

        # 过滤后点数
        if "n_points_filtered" in features_point_cloud:
            for i, n_pts in enumerate(features_point_cloud["n_points_filtered"]):
                summary[f"PC_NPoints_Filtered_H{i}"] = n_pts

        # 缩放因子
        if "scale_factor" in features_point_cloud:
            for i, scale in enumerate(features_point_cloud["scale_factor"]):
                summary[f"PC_Scale_Factor_H{i}"] = scale

        # 距离度量特征
        distance_metrics = ['bottleneck', 'wasserstein', 'landscape']
        for metric in distance_metrics:
            if f"self_distance_{metric}_mean" in features_point_cloud:
                summary[f"PC_SelfDist_{metric}_Mean"] = features_point_cloud[f"self_distance_{metric}_mean"]
                summary[f"PC_SelfDist_{metric}_Std"] = features_point_cloud[f"self_distance_{metric}_std"]

        # Betti曲线统计
        for i in range(min(3, features_point_cloud["betti"].shape[0])):
            betti_curve = features_point_cloud["betti"][i]
            summary[f"PC_Betti_H{i}_Max"] = np.max(betti_curve)
            summary[f"PC_Betti_H{i}_Mean"] = np.mean(betti_curve)
            summary[f"PC_Betti_H{i}_AUC"] = np.trapz(betti_curve)
            summary[f"PC_Betti_H{i}_Std"] = np.std(betti_curve)

        # 持续景观统计
        if "landscape" in features_point_cloud:
            landscape_data = features_point_cloud["landscape"]
            for i in range(min(3, landscape_data.shape[0] // 3)):  # 每个同调维度3层
                for layer in range(3):
                    idx = i * 3 + layer
                    if idx < landscape_data.shape[0]:
                        layer_data = landscape_data[idx]
                        summary[f"PC_Landscape_H{i}_L{layer}_Max"] = np.max(layer_data)
                        summary[f"PC_Landscape_H{i}_L{layer}_Mean"] = np.mean(layer_data)
                        summary[f"PC_Landscape_H{i}_L{layer}_AUC"] = np.trapz(layer_data)

        # 热核表示统计
        if "heat" in features_point_cloud:
            heat_data = features_point_cloud["heat"]
            for i in range(min(3, heat_data.shape[0])):
                heat_matrix = heat_data[i]
                summary[f"PC_Heat_H{i}_Max"] = np.max(heat_matrix)
                summary[f"PC_Heat_H{i}_Mean"] = np.mean(heat_matrix)
                summary[f"PC_Heat_H{i}_Norm"] = np.linalg.norm(heat_matrix)

        # 轮廓表示统计
        if "silhouette" in features_point_cloud:
            silhouette_data = features_point_cloud["silhouette"]
            for i in range(min(3, silhouette_data.shape[0])):
                silhouette_curve = silhouette_data[i]
                summary[f"PC_Silhouette_H{i}_Max"] = np.max(silhouette_curve)
                summary[f"PC_Silhouette_H{i}_Mean"] = np.mean(silhouette_curve)
                summary[f"PC_Silhouette_H{i}_AUC"] = np.trapz(silhouette_curve)

        # 持续图像统计
        if "persistence_image" in features_point_cloud:
            image_data = features_point_cloud["persistence_image"]
            for i in range(min(3, image_data.shape[0])):
                image_matrix = image_data[i]
                summary[f"PC_PersImage_H{i}_Max"] = np.max(image_matrix)
                summary[f"PC_PersImage_H{i}_Mean"] = np.mean(image_matrix)
                summary[f"PC_PersImage_H{i}_Norm"] = np.linalg.norm(image_matrix)

        # 复多项式系数统计
        if "complex_polynomial" in features_point_cloud:
            poly_data = features_point_cloud["complex_polynomial"]
            # 每个同调维度有10个系数（5个实部+5个虚部）
            for i in range(3):
                start_idx = i * 10
                if start_idx + 10 <= len(poly_data):
                    real_coeffs = poly_data[start_idx:start_idx+5]
                    imag_coeffs = poly_data[start_idx+5:start_idx+10]
                    summary[f"PC_Poly_H{i}_Real_Mean"] = np.mean(real_coeffs)
                    summary[f"PC_Poly_H{i}_Imag_Mean"] = np.mean(imag_coeffs)
                    summary[f"PC_Poly_H{i}_Real_Norm"] = np.linalg.norm(real_coeffs)
                    summary[f"PC_Poly_H{i}_Imag_Norm"] = np.linalg.norm(imag_coeffs)

    # 立方方法特征 (类似的处理)
    if features_cubical is not None:
        # 持续熵
        for i, entropy in enumerate(features_cubical["entropy"]):
            summary[f"CB_Entropy_H{i}"] = entropy

        # 多种振幅指标
        amplitude_metrics = ["wasserstein", "bottleneck", "landscape", "betti"]
        for metric in amplitude_metrics:
            if f"amplitude_{metric}" in features_cubical:
                for i, amp in enumerate(features_cubical[f"amplitude_{metric}"]):
                    summary[f"CB_Amplitude_{metric}_H{i}"] = amp

        # 点数
        for i, n_pts in enumerate(features_cubical["n_points"]):
            summary[f"CB_NPoints_H{i}"] = n_pts

        # 过滤后点数
        if "n_points_filtered" in features_cubical:
            for i, n_pts in enumerate(features_cubical["n_points_filtered"]):
                summary[f"CB_NPoints_Filtered_H{i}"] = n_pts

        # 缩放因子
        if "scale_factor" in features_cubical:
            for i, scale in enumerate(features_cubical["scale_factor"]):
                summary[f"CB_Scale_Factor_H{i}"] = scale

        # 距离度量特征
        distance_metrics = ['bottleneck', 'wasserstein', 'landscape']
        for metric in distance_metrics:
            if f"self_distance_{metric}_mean" in features_cubical:
                summary[f"CB_SelfDist_{metric}_Mean"] = features_cubical[f"self_distance_{metric}_mean"]
                summary[f"CB_SelfDist_{metric}_Std"] = features_cubical[f"self_distance_{metric}_std"]

        # Betti曲线统计
        for i in range(min(3, features_cubical["betti"].shape[0])):
            betti_curve = features_cubical["betti"][i]
            summary[f"CB_Betti_H{i}_Max"] = np.max(betti_curve)
            summary[f"CB_Betti_H{i}_Mean"] = np.mean(betti_curve)
            summary[f"CB_Betti_H{i}_AUC"] = np.trapz(betti_curve)
            summary[f"CB_Betti_H{i}_Std"] = np.std(betti_curve)

        # 持续景观统计
        if "landscape" in features_cubical:
            landscape_data = features_cubical["landscape"]
            for i in range(min(3, landscape_data.shape[0] // 3)):
                for layer in range(3):
                    idx = i * 3 + layer
                    if idx < landscape_data.shape[0]:
                        layer_data = landscape_data[idx]
                        summary[f"CB_Landscape_H{i}_L{layer}_Max"] = np.max(layer_data)
                        summary[f"CB_Landscape_H{i}_L{layer}_Mean"] = np.mean(layer_data)
                        summary[f"CB_Landscape_H{i}_L{layer}_AUC"] = np.trapz(layer_data)

        # 热核表示统计
        if "heat" in features_cubical:
            heat_data = features_cubical["heat"]
            for i in range(min(3, heat_data.shape[0])):
                heat_matrix = heat_data[i]
                summary[f"CB_Heat_H{i}_Max"] = np.max(heat_matrix)
                summary[f"CB_Heat_H{i}_Mean"] = np.mean(heat_matrix)
                summary[f"CB_Heat_H{i}_Norm"] = np.linalg.norm(heat_matrix)

        # 轮廓表示统计
        if "silhouette" in features_cubical:
            silhouette_data = features_cubical["silhouette"]
            for i in range(min(3, silhouette_data.shape[0])):
                silhouette_curve = silhouette_data[i]
                summary[f"CB_Silhouette_H{i}_Max"] = np.max(silhouette_curve)
                summary[f"CB_Silhouette_H{i}_Mean"] = np.mean(silhouette_curve)
                summary[f"CB_Silhouette_H{i}_AUC"] = np.trapz(silhouette_curve)

        # 持续图像统计
        if "persistence_image" in features_cubical:
            image_data = features_cubical["persistence_image"]
            for i in range(min(3, image_data.shape[0])):
                image_matrix = image_data[i]
                summary[f"CB_PersImage_H{i}_Max"] = np.max(image_matrix)
                summary[f"CB_PersImage_H{i}_Mean"] = np.mean(image_matrix)
                summary[f"CB_PersImage_H{i}_Norm"] = np.linalg.norm(image_matrix)

        # 复多项式系数统计
        if "complex_polynomial" in features_cubical:
            poly_data = features_cubical["complex_polynomial"]
            for i in range(3):
                start_idx = i * 10
                if start_idx + 10 <= len(poly_data):
                    real_coeffs = poly_data[start_idx:start_idx+5]
                    imag_coeffs = poly_data[start_idx+5:start_idx+10]
                    summary[f"CB_Poly_H{i}_Real_Mean"] = np.mean(real_coeffs)
                    summary[f"CB_Poly_H{i}_Imag_Mean"] = np.mean(imag_coeffs)
                    summary[f"CB_Poly_H{i}_Real_Norm"] = np.linalg.norm(real_coeffs)
                    summary[f"CB_Poly_H{i}_Imag_Norm"] = np.linalg.norm(imag_coeffs)

    return summary


def find_hcc_matching_files(image_dir, mask_dir):
    """查找HCC数据集中匹配的image-mask文件对"""
    print(f"搜索图像文件: {image_dir}")
    print(f"搜索mask文件: {mask_dir}")

    if not os.path.exists(image_dir):
        print(f"❌ 图像目录不存在: {image_dir}")
        return []

    if not os.path.exists(mask_dir):
        print(f"❌ Mask目录不存在: {mask_dir}")
        return []

    image_files = glob.glob(os.path.join(image_dir, "*.nii.gz"))
    mask_files = glob.glob(os.path.join(mask_dir, "*.nii.gz"))

    print(f"找到 {len(image_files)} 个图像文件")
    print(f"找到 {len(mask_files)} 个mask文件")

    matched_pairs = []

    for img_path in image_files:
        img_basename = os.path.basename(img_path)
        img_name = os.path.splitext(os.path.splitext(img_basename)[0])[0]

        expected_mask_name = img_name + "-mask.nii.gz"
        expected_mask_path = os.path.join(mask_dir, expected_mask_name)

        if os.path.exists(expected_mask_path):
            matched_pairs.append((img_path, expected_mask_path))
            print(f"✅ 匹配: {img_basename} -> {expected_mask_name}")
        else:
            print(f"❌ 未找到对应mask: {img_basename}")

    print(f"\n匹配结果: 成功匹配 {len(matched_pairs)} 对文件")
    return matched_pairs


def analyze_single_hcc_file(image_path, mask_path):
    """分析单个HCC文件"""
    start_time = datetime.now()

    print(f"\n开始分析: {os.path.basename(image_path)}")

    # 1. 加载图像和mask
    image_data, mask_data = load_image_and_mask(image_path, mask_path)
    if image_data is None or mask_data is None:
        return None

    # 2. 提取肿瘤区域
    binary_image, tumor_mask = extract_tumor_region(image_data, mask_data, MASK_THRESHOLD)
    if binary_image is None:
        print(f"❌ 肿瘤区域为空: {os.path.basename(image_path)}")
        return None

    # 3. 转换为点云
    point_cloud = image_to_point_cloud(binary_image, MAX_POINTS)
    if point_cloud is None:
        return None

    # 4. 计算持续同调
    diagrams_point_cloud = compute_persistent_homology_point_cloud(point_cloud, MAX_EDGE_LENGTH, N_WORKERS)
    diagrams_cubical = compute_persistent_homology_cubical(binary_image, N_WORKERS)

    # 5. 提取拓扑特征
    features_point_cloud = compute_topological_features(diagrams_point_cloud)
    features_cubical = compute_topological_features(diagrams_cubical)

    # 6. 准备图像信息
    processing_time = (datetime.now() - start_time).total_seconds()

    image_info = {
        "File_Name": os.path.basename(image_path),
        "Mask_Name": os.path.basename(mask_path),
        "Image_Shape": f"{image_data.shape[0]}x{image_data.shape[1]}x{image_data.shape[2]}",
        "Tumor_Voxels": int(np.sum(tumor_mask)),
        "Total_Voxels": int(tumor_mask.size),
        "Tumor_Ratio": np.sum(tumor_mask) / tumor_mask.size,
        "Processing_Time": processing_time,
        "Point_Cloud_Size": len(point_cloud) if point_cloud is not None else 0,
        "Analysis_Date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }

    # 7. 提取汇总特征
    summary_features = extract_summary_features(features_point_cloud, features_cubical, image_info)

    print(f"✅ 分析完成: {os.path.basename(image_path)} ({processing_time:.2f}秒)")

    return {
        "summary_features": summary_features,
        "features_point_cloud": features_point_cloud,
        "features_cubical": features_cubical,
        "image_data": image_data,
        "tumor_mask": tumor_mask,
        "point_cloud": point_cloud
    }


def batch_analyze_hcc():
    """批量分析HCC数据"""
    print("\n开始批量分析...")

    # 查找匹配的文件对
    matched_pairs = find_hcc_matching_files(IMAGE_DIR, MASK_DIR)

    if not matched_pairs:
        print("❌ 未找到任何匹配的文件对!")
        return None

    # 批量处理
    all_results = []
    failed_files = []

    for i, (image_path, mask_path) in enumerate(matched_pairs):
        print(f"\n进度: {i+1}/{len(matched_pairs)}")

        result = analyze_single_hcc_file(image_path, mask_path)
        if result is not None:
            all_results.append(result)
        else:
            failed_files.append(image_path)

    print(f"\n批量分析完成!")
    print(f"  - 成功: {len(all_results)} 个文件")
    print(f"  - 失败: {len(failed_files)} 个文件")

    return all_results, failed_files


def save_results_to_excel(all_results, output_dir):
    """保存结果到Excel文件"""
    print(f"\n保存结果到Excel...")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 提取汇总特征
    summary_features = [result["summary_features"] for result in all_results]

    # 创建DataFrame
    df = pd.DataFrame(summary_features)

    # 保存到Excel
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    excel_file = os.path.join(output_dir, f"HCC_TDA_Features_{timestamp}.xlsx")

    try:
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            # 主要特征
            df.to_excel(writer, sheet_name='HCC_TDA_Features', index=False)

            # 特征描述
            feature_descriptions = create_feature_descriptions()
            desc_df = pd.DataFrame(list(feature_descriptions.items()),
                                 columns=['Feature_Name', 'Description'])
            desc_df.to_excel(writer, sheet_name='Feature_Descriptions', index=False)

            # 统计信息
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            if len(numeric_columns) > 0:
                stats_df = df[numeric_columns].describe()
                stats_df.to_excel(writer, sheet_name='Statistics')

        print(f"✅ Excel文件已保存: {excel_file}")
        return excel_file, df

    except Exception as e:
        print(f"❌ 保存Excel失败: {e}")
        return None, df


def create_feature_descriptions():
    """创建特征描述"""
    descriptions = {
        "File_Name": "图像文件名",
        "Mask_Name": "对应的mask文件名",
        "Image_Shape": "图像尺寸",
        "Tumor_Voxels": "肿瘤区域体素数量",
        "Total_Voxels": "总体素数量",
        "Tumor_Ratio": "肿瘤体素占总体素的比例",
        "Processing_Time": "处理时间(秒)",
        "Point_Cloud_Size": "点云大小",
        "Analysis_Date": "分析日期",

        # 基础TDA特征
        "PC_Entropy_H0": "点云方法 - H0维持续熵",
        "PC_Entropy_H1": "点云方法 - H1维持续熵",
        "PC_Entropy_H2": "点云方法 - H2维持续熵",
        "CB_Entropy_H0": "立方方法 - H0维持续熵",
        "CB_Entropy_H1": "立方方法 - H1维持续熵",
        "CB_Entropy_H2": "立方方法 - H2维持续熵",

        # 多种振幅指标
        "PC_Amplitude_wasserstein_H0": "点云方法 - H0维Wasserstein振幅",
        "PC_Amplitude_wasserstein_H1": "点云方法 - H1维Wasserstein振幅",
        "PC_Amplitude_wasserstein_H2": "点云方法 - H2维Wasserstein振幅",
        "PC_Amplitude_bottleneck_H0": "点云方法 - H0维Bottleneck振幅",
        "PC_Amplitude_bottleneck_H1": "点云方法 - H1维Bottleneck振幅",
        "PC_Amplitude_bottleneck_H2": "点云方法 - H2维Bottleneck振幅",
        "PC_Amplitude_landscape_H0": "点云方法 - H0维Landscape振幅",
        "PC_Amplitude_landscape_H1": "点云方法 - H1维Landscape振幅",
        "PC_Amplitude_landscape_H2": "点云方法 - H2维Landscape振幅",
        "PC_Amplitude_betti_H0": "点云方法 - H0维Betti振幅",
        "PC_Amplitude_betti_H1": "点云方法 - H1维Betti振幅",
        "PC_Amplitude_betti_H2": "点云方法 - H2维Betti振幅",

        # 点数特征
        "PC_NPoints_H0": "点云方法 - H0维点数",
        "PC_NPoints_H1": "点云方法 - H1维点数",
        "PC_NPoints_H2": "点云方法 - H2维点数",

        # Betti曲线特征
        "PC_Betti_H0_Max": "点云方法 - H0维Betti曲线最大值",
        "PC_Betti_H0_Mean": "点云方法 - H0维Betti曲线均值",
        "PC_Betti_H0_AUC": "点云方法 - H0维Betti曲线面积",
        "PC_Betti_H0_Std": "点云方法 - H0维Betti曲线标准差",
        "PC_Betti_H1_Max": "点云方法 - H1维Betti曲线最大值",
        "PC_Betti_H1_Mean": "点云方法 - H1维Betti曲线均值",
        "PC_Betti_H1_AUC": "点云方法 - H1维Betti曲线面积",
        "PC_Betti_H1_Std": "点云方法 - H1维Betti曲线标准差",
        "PC_Betti_H2_Max": "点云方法 - H2维Betti曲线最大值",
        "PC_Betti_H2_Mean": "点云方法 - H2维Betti曲线均值",
        "PC_Betti_H2_AUC": "点云方法 - H2维Betti曲线面积",
        "PC_Betti_H2_Std": "点云方法 - H2维Betti曲线标准差",

        # 持续景观特征
        "PC_Landscape_H0_L0_Max": "点云方法 - H0维第0层景观最大值",
        "PC_Landscape_H0_L0_Mean": "点云方法 - H0维第0层景观均值",
        "PC_Landscape_H0_L0_AUC": "点云方法 - H0维第0层景观面积",
        "PC_Landscape_H1_L0_Max": "点云方法 - H1维第0层景观最大值",
        "PC_Landscape_H1_L0_Mean": "点云方法 - H1维第0层景观均值",
        "PC_Landscape_H1_L0_AUC": "点云方法 - H1维第0层景观面积",

        # 热核表示特征
        "PC_Heat_H0_Max": "点云方法 - H0维热核最大值",
        "PC_Heat_H0_Mean": "点云方法 - H0维热核均值",
        "PC_Heat_H0_Norm": "点云方法 - H0维热核范数",
        "PC_Heat_H1_Max": "点云方法 - H1维热核最大值",
        "PC_Heat_H1_Mean": "点云方法 - H1维热核均值",
        "PC_Heat_H1_Norm": "点云方法 - H1维热核范数",

        # 轮廓表示特征
        "PC_Silhouette_H0_Max": "点云方法 - H0维轮廓最大值",
        "PC_Silhouette_H0_Mean": "点云方法 - H0维轮廓均值",
        "PC_Silhouette_H0_AUC": "点云方法 - H0维轮廓面积",
        "PC_Silhouette_H1_Max": "点云方法 - H1维轮廓最大值",
        "PC_Silhouette_H1_Mean": "点云方法 - H1维轮廓均值",
        "PC_Silhouette_H1_AUC": "点云方法 - H1维轮廓面积",

        # 持续图像特征
        "PC_PersImage_H0_Max": "点云方法 - H0维持续图像最大值",
        "PC_PersImage_H0_Mean": "点云方法 - H0维持续图像均值",
        "PC_PersImage_H0_Norm": "点云方法 - H0维持续图像范数",
        "PC_PersImage_H1_Max": "点云方法 - H1维持续图像最大值",
        "PC_PersImage_H1_Mean": "点云方法 - H1维持续图像均值",
        "PC_PersImage_H1_Norm": "点云方法 - H1维持续图像范数",

        # 复多项式特征
        "PC_Poly_H0_Real_Mean": "点云方法 - H0维复多项式实部均值",
        "PC_Poly_H0_Imag_Mean": "点云方法 - H0维复多项式虚部均值",
        "PC_Poly_H0_Real_Norm": "点云方法 - H0维复多项式实部范数",
        "PC_Poly_H0_Imag_Norm": "点云方法 - H0维复多项式虚部范数",
        "PC_Poly_H1_Real_Mean": "点云方法 - H1维复多项式实部均值",
        "PC_Poly_H1_Imag_Mean": "点云方法 - H1维复多项式虚部均值",
        "PC_Poly_H1_Real_Norm": "点云方法 - H1维复多项式实部范数",
        "PC_Poly_H1_Imag_Norm": "点云方法 - H1维复多项式虚部范数",
    }

    # 为立方方法添加类似的特征描述（将PC_替换为CB_）
    cb_descriptions = {}
    for key, value in descriptions.items():
        if key.startswith("PC_"):
            cb_key = key.replace("PC_", "CB_")
            cb_value = value.replace("点云方法", "立方方法")
            cb_descriptions[cb_key] = cb_value

    descriptions.update(cb_descriptions)
    return descriptions


def generate_visualizations(all_results, df, output_dir):
    """生成可视化图表"""
    print(f"\n生成可视化图表...")

    try:
        # 1. 特征分布图
        plot_feature_distributions(df, output_dir)

        # 2. 相关性分析图
        plot_correlation_analysis(df, output_dir)

        # 3. PCA分析图
        plot_pca_analysis(df, output_dir)

        # 4. 肿瘤统计图
        plot_tumor_statistics(df, output_dir)

        # 5. 拓扑特征比较图
        plot_topological_features(df, output_dir)

        # 6. TDA专用可视化图表
        plot_tda_visualizations(all_results, output_dir)

        print(f"✅ 可视化图表已保存到: {output_dir}")

    except Exception as e:
        print(f"❌ 生成可视化失败: {e}")


def plot_feature_distributions(df, output_dir):
    """绘制特征分布图"""
    # 基本统计特征
    basic_features = ['Tumor_Ratio', 'Tumor_Voxels', 'Point_Cloud_Size', 'Processing_Time']
    basic_features = [f for f in basic_features if f in df.columns]

    if basic_features:
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        axes = axes.flatten()

        for i, feature in enumerate(basic_features[:4]):
            if i < len(axes):
                axes[i].hist(df[feature], bins=15, alpha=0.7, edgecolor='black')
                axes[i].set_title(f'{feature} 分布')
                axes[i].set_xlabel(feature)
                axes[i].set_ylabel('频数')

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'basic_features_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()


def plot_correlation_analysis(df, output_dir):
    """绘制相关性分析图"""
    numeric_cols = df.select_dtypes(include=[np.number]).columns

    if len(numeric_cols) > 1:
        # 选择主要的拓扑特征
        topo_features = [col for col in numeric_cols if any(x in col for x in ['Entropy', 'Amplitude', 'Betti'])]

        if len(topo_features) > 1:
            corr_matrix = df[topo_features].corr()

            plt.figure(figsize=(12, 10))
            sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0,
                       square=True, linewidths=0.5, fmt='.2f')
            plt.title('拓扑特征相关性矩阵')
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'correlation_matrix.png'), dpi=300, bbox_inches='tight')
            plt.close()


def plot_pca_analysis(df, output_dir):
    """绘制PCA分析图"""
    numeric_cols = df.select_dtypes(include=[np.number]).columns

    if len(numeric_cols) > 2:
        # 数据标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(df[numeric_cols])

        # PCA分析
        pca = PCA()
        X_pca = pca.fit_transform(X_scaled)

        # 绘制解释方差比例
        plt.figure(figsize=(12, 5))

        plt.subplot(1, 2, 1)
        plt.plot(range(1, min(11, len(pca.explained_variance_ratio_) + 1)),
                pca.explained_variance_ratio_[:10], 'bo-')
        plt.xlabel('主成分')
        plt.ylabel('解释方差比例')
        plt.title('PCA解释方差比例')
        plt.grid(True, alpha=0.3)

        plt.subplot(1, 2, 2)
        plt.scatter(X_pca[:, 0], X_pca[:, 1], alpha=0.6)
        plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} 方差)')
        plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} 方差)')
        plt.title('前两个主成分散点图')
        plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'pca_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()


def plot_tumor_statistics(df, output_dir):
    """绘制肿瘤统计图"""
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))

    # 肿瘤比例分布
    if 'Tumor_Ratio' in df.columns:
        axes[0, 0].hist(df['Tumor_Ratio'], bins=15, alpha=0.7, edgecolor='black')
        axes[0, 0].set_title('肿瘤比例分布')
        axes[0, 0].set_xlabel('肿瘤比例')
        axes[0, 0].set_ylabel('频数')

    # 肿瘤体素数分布
    if 'Tumor_Voxels' in df.columns:
        axes[0, 1].hist(df['Tumor_Voxels'], bins=15, alpha=0.7, edgecolor='black')
        axes[0, 1].set_title('肿瘤体素数分布')
        axes[0, 1].set_xlabel('肿瘤体素数')
        axes[0, 1].set_ylabel('频数')

    # 肿瘤比例 vs 处理时间
    if 'Tumor_Ratio' in df.columns and 'Processing_Time' in df.columns:
        axes[1, 0].scatter(df['Tumor_Ratio'], df['Processing_Time'], alpha=0.6)
        axes[1, 0].set_xlabel('肿瘤比例')
        axes[1, 0].set_ylabel('处理时间(秒)')
        axes[1, 0].set_title('肿瘤比例 vs 处理时间')

    # 肿瘤体素数 vs 点云大小
    if 'Tumor_Voxels' in df.columns and 'Point_Cloud_Size' in df.columns:
        axes[1, 1].scatter(df['Tumor_Voxels'], df['Point_Cloud_Size'], alpha=0.6)
        axes[1, 1].set_xlabel('肿瘤体素数')
        axes[1, 1].set_ylabel('点云大小')
        axes[1, 1].set_title('肿瘤体素数 vs 点云大小')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'tumor_statistics.png'), dpi=300, bbox_inches='tight')
    plt.close()


def plot_topological_features(df, output_dir):
    """绘制拓扑特征比较图"""
    # 持续熵比较
    entropy_features = [col for col in df.columns if 'Entropy' in col]
    if entropy_features:
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))

        # 点云方法 vs 立方方法
        pc_entropy = [col for col in entropy_features if col.startswith('PC_')]
        cb_entropy = [col for col in entropy_features if col.startswith('CB_')]

        if pc_entropy and cb_entropy:
            x = np.arange(len(pc_entropy))
            width = 0.35

            axes[0].bar(x - width/2, df[pc_entropy].mean(), width,
                       label='点云方法', alpha=0.8)
            axes[0].bar(x + width/2, df[cb_entropy].mean(), width,
                       label='立方方法', alpha=0.8)
            axes[0].set_xlabel('同调维度')
            axes[0].set_ylabel('平均持续熵')
            axes[0].set_title('持续熵比较')
            axes[0].set_xticks(x)
            axes[0].set_xticklabels(['H0', 'H1', 'H2'])
            axes[0].legend()

        # 振幅比较
        amplitude_features = [col for col in df.columns if 'Amplitude' in col]
        pc_amplitude = [col for col in amplitude_features if col.startswith('PC_')]
        cb_amplitude = [col for col in amplitude_features if col.startswith('CB_')]

        if pc_amplitude and cb_amplitude:
            x = np.arange(len(pc_amplitude))

            axes[1].bar(x - width/2, df[pc_amplitude].mean(), width,
                       label='点云方法', alpha=0.8)
            axes[1].bar(x + width/2, df[cb_amplitude].mean(), width,
                       label='立方方法', alpha=0.8)
            axes[1].set_xlabel('同调维度')
            axes[1].set_ylabel('平均振幅')
            axes[1].set_title('振幅比较')
            axes[1].set_xticks(x)
            axes[1].set_xticklabels(['H0', 'H1', 'H2'])
            axes[1].legend()

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'topological_features_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()


def plot_tda_visualizations(all_results, output_dir):
    """绘制TDA专用可视化图表"""
    print("  生成TDA专用可视化图表...")

    if not all_results:
        print("  ❌ 没有可用的分析结果")
        return

    try:
        # 选择第一个样本进行可视化演示
        sample_result = all_results[0]
        sample_name = sample_result["summary_features"]["File_Name"]

        # 1. 持续图可视化
        plot_persistence_diagrams(sample_result, output_dir, sample_name)

        # 2. Betti曲线可视化
        plot_betti_curves_visualization(sample_result, output_dir, sample_name)

        # 3. 点云可视化
        plot_point_cloud_visualization(sample_result, output_dir, sample_name)

        # 4. 热核和持续图像可视化
        plot_heat_and_persistence_images(sample_result, output_dir, sample_name)

        # 5. 多样本Betti曲线比较
        plot_multi_sample_betti_comparison(all_results, output_dir)

        print("  ✅ TDA专用可视化图表生成完成")

    except Exception as e:
        print(f"  ❌ TDA可视化生成失败: {e}")


def plot_persistence_diagrams(sample_result, output_dir, sample_name):
    """绘制持续图"""
    try:
        # 获取持续同调图数据
        features_pc = sample_result.get("features_point_cloud")
        features_cb = sample_result.get("features_cubical")

        # 这里需要原始的持续图数据，但当前代码中没有保存
        # 作为替代，我们创建一个示例说明图
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))

        # 点云方法持续图说明
        axes[0].text(0.5, 0.5, f'点云方法持续图\n样本: {sample_name}\n\n'
                    f'H0维点数: {features_pc["n_points"][0] if features_pc else "N/A"}\n'
                    f'H1维点数: {features_pc["n_points"][1] if features_pc else "N/A"}\n'
                    f'H2维点数: {features_pc["n_points"][2] if features_pc else "N/A"}',
                    ha='center', va='center', fontsize=12,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
        axes[0].set_title('点云方法持续图')
        axes[0].set_xlim(0, 1)
        axes[0].set_ylim(0, 1)
        axes[0].axis('off')

        # 立方方法持续图说明
        axes[1].text(0.5, 0.5, f'立方方法持续图\n样本: {sample_name}\n\n'
                    f'H0维点数: {features_cb["n_points"][0] if features_cb else "N/A"}\n'
                    f'H1维点数: {features_cb["n_points"][1] if features_cb else "N/A"}\n'
                    f'H2维点数: {features_cb["n_points"][2] if features_cb else "N/A"}',
                    ha='center', va='center', fontsize=12,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen"))
        axes[1].set_title('立方方法持续图')
        axes[1].set_xlim(0, 1)
        axes[1].set_ylim(0, 1)
        axes[1].axis('off')

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'persistence_diagrams_{sample_name}.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    except Exception as e:
        print(f"    持续图绘制失败: {e}")


def plot_betti_curves_visualization(sample_result, output_dir, sample_name):
    """绘制Betti曲线可视化"""
    try:
        features_pc = sample_result.get("features_point_cloud")
        features_cb = sample_result.get("features_cubical")

        if features_pc and "betti" in features_pc:
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))

            # 点云方法Betti曲线
            betti_pc = features_pc["betti"]
            for i in range(min(3, betti_pc.shape[0])):
                axes[0, i].plot(betti_pc[i], linewidth=2, label=f'PC H{i}')
                axes[0, i].set_title(f'点云方法 - H{i}维Betti曲线')
                axes[0, i].set_xlabel('滤波参数')
                axes[0, i].set_ylabel('Betti数')
                axes[0, i].grid(True, alpha=0.3)
                axes[0, i].legend()

            # 立方方法Betti曲线
            if features_cb and "betti" in features_cb:
                betti_cb = features_cb["betti"]
                for i in range(min(3, betti_cb.shape[0])):
                    axes[1, i].plot(betti_cb[i], linewidth=2, label=f'CB H{i}', color='orange')
                    axes[1, i].set_title(f'立方方法 - H{i}维Betti曲线')
                    axes[1, i].set_xlabel('滤波参数')
                    axes[1, i].set_ylabel('Betti数')
                    axes[1, i].grid(True, alpha=0.3)
                    axes[1, i].legend()

            plt.suptitle(f'Betti曲线分析 - {sample_name}', fontsize=16)
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f'betti_curves_{sample_name}.png'),
                       dpi=300, bbox_inches='tight')
            plt.close()

    except Exception as e:
        print(f"    Betti曲线绘制失败: {e}")


def plot_point_cloud_visualization(sample_result, output_dir, sample_name):
    """绘制点云可视化"""
    try:
        point_cloud = sample_result.get("point_cloud")
        tumor_mask = sample_result.get("tumor_mask")

        if point_cloud is not None and len(point_cloud) > 0:
            fig = plt.figure(figsize=(15, 5))

            # 3D点云散点图
            ax1 = fig.add_subplot(131, projection='3d')
            if len(point_cloud) > 1000:
                # 如果点太多，随机采样显示
                indices = np.random.choice(len(point_cloud), 1000, replace=False)
                pc_sample = point_cloud[indices]
            else:
                pc_sample = point_cloud

            ax1.scatter(pc_sample[:, 0], pc_sample[:, 1], pc_sample[:, 2],
                       c='blue', alpha=0.6, s=1)
            ax1.set_title(f'3D点云\n({len(point_cloud)}个点)')
            ax1.set_xlabel('X')
            ax1.set_ylabel('Y')
            ax1.set_zlabel('Z')

            # XY平面投影
            ax2 = fig.add_subplot(132)
            ax2.scatter(pc_sample[:, 0], pc_sample[:, 1], c='red', alpha=0.6, s=1)
            ax2.set_title('XY平面投影')
            ax2.set_xlabel('X')
            ax2.set_ylabel('Y')
            ax2.grid(True, alpha=0.3)

            # XZ平面投影
            ax3 = fig.add_subplot(133)
            ax3.scatter(pc_sample[:, 0], pc_sample[:, 2], c='green', alpha=0.6, s=1)
            ax3.set_title('XZ平面投影')
            ax3.set_xlabel('X')
            ax3.set_ylabel('Z')
            ax3.grid(True, alpha=0.3)

            plt.suptitle(f'点云可视化 - {sample_name}', fontsize=16)
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f'point_cloud_{sample_name}.png'),
                       dpi=300, bbox_inches='tight')
            plt.close()

    except Exception as e:
        print(f"    点云可视化失败: {e}")


def plot_heat_and_persistence_images(sample_result, output_dir, sample_name):
    """绘制热核和持续图像可视化"""
    try:
        features_pc = sample_result.get("features_point_cloud")

        if features_pc:
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))

            # 热核表示可视化
            if "heat" in features_pc:
                heat_data = features_pc["heat"]
                for i in range(min(3, heat_data.shape[0])):
                    im1 = axes[0, i].imshow(heat_data[i], cmap='viridis', aspect='auto')
                    axes[0, i].set_title(f'热核表示 - H{i}维')
                    axes[0, i].set_xlabel('Birth')
                    axes[0, i].set_ylabel('Death')
                    plt.colorbar(im1, ax=axes[0, i])

            # 持续图像可视化
            if "persistence_image" in features_pc:
                image_data = features_pc["persistence_image"]
                for i in range(min(3, image_data.shape[0])):
                    im2 = axes[1, i].imshow(image_data[i], cmap='plasma', aspect='auto')
                    axes[1, i].set_title(f'持续图像 - H{i}维')
                    axes[1, i].set_xlabel('Birth')
                    axes[1, i].set_ylabel('Persistence')
                    plt.colorbar(im2, ax=axes[1, i])

            plt.suptitle(f'热核与持续图像 - {sample_name}', fontsize=16)
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f'heat_persistence_images_{sample_name}.png'),
                       dpi=300, bbox_inches='tight')
            plt.close()

    except Exception as e:
        print(f"    热核和持续图像可视化失败: {e}")


def plot_multi_sample_betti_comparison(all_results, output_dir):
    """绘制多样本Betti曲线比较"""
    try:
        if len(all_results) < 2:
            print("    样本数量不足，跳过多样本比较")
            return

        fig, axes = plt.subplots(1, 3, figsize=(18, 6))

        # 收集所有样本的Betti曲线数据
        for idx, result in enumerate(all_results[:5]):  # 最多显示5个样本
            features_pc = result.get("features_point_cloud")
            if features_pc and "betti" in features_pc:
                betti_data = features_pc["betti"]
                sample_name = result["summary_features"]["File_Name"]

                for i in range(min(3, betti_data.shape[0])):
                    axes[i].plot(betti_data[i], alpha=0.7, linewidth=2,
                               label=f'{sample_name[:10]}...')
                    axes[i].set_title(f'H{i}维Betti曲线比较')
                    axes[i].set_xlabel('滤波参数')
                    axes[i].set_ylabel('Betti数')
                    axes[i].grid(True, alpha=0.3)
                    axes[i].legend()

        plt.suptitle('多样本Betti曲线比较', fontsize=16)
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'multi_sample_betti_comparison.png'),
                   dpi=300, bbox_inches='tight')
        plt.close()

    except Exception as e:
        print(f"    多样本Betti曲线比较失败: {e}")


def generate_analysis_report(df, output_dir):
    """生成分析报告"""
    print(f"\n生成分析报告...")

    report_file = os.path.join(output_dir, "HCC_TDA_Analysis_Report.txt")

    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("HCC肿瘤3D图像TDA分析报告\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"分析文件数量: {len(df)}\n")
            f.write(f"特征数量: {len(df.columns)}\n\n")

            # 肿瘤区域统计
            f.write("肿瘤区域统计:\n")
            f.write("-" * 20 + "\n")

            if 'Tumor_Ratio' in df.columns:
                tumor_ratios = df['Tumor_Ratio']
                f.write(f"肿瘤比例统计:\n")
                f.write(f"  平均值: {tumor_ratios.mean():.4f}\n")
                f.write(f"  标准差: {tumor_ratios.std():.4f}\n")
                f.write(f"  最小值: {tumor_ratios.min():.4f}\n")
                f.write(f"  最大值: {tumor_ratios.max():.4f}\n")
                f.write(f"  中位数: {tumor_ratios.median():.4f}\n\n")

            if 'Tumor_Voxels' in df.columns:
                tumor_voxels = df['Tumor_Voxels']
                f.write(f"肿瘤体素数统计:\n")
                f.write(f"  平均值: {tumor_voxels.mean():.0f}\n")
                f.write(f"  标准差: {tumor_voxels.std():.0f}\n")
                f.write(f"  最小值: {tumor_voxels.min():.0f}\n")
                f.write(f"  最大值: {tumor_voxels.max():.0f}\n")
                f.write(f"  中位数: {tumor_voxels.median():.0f}\n\n")

            # 拓扑特征统计
            f.write("拓扑特征统计:\n")
            f.write("-" * 20 + "\n")

            # 持续熵统计
            entropy_features = [col for col in df.columns if 'Entropy' in col]
            if entropy_features:
                f.write("持续熵特征:\n")
                for feature in entropy_features:
                    values = df[feature]
                    f.write(f"  {feature}: {values.mean():.4f} ± {values.std():.4f}\n")
                f.write("\n")

            # 振幅统计
            amplitude_features = [col for col in df.columns if 'Amplitude' in col]
            if amplitude_features:
                f.write("振幅特征:\n")
                for feature in amplitude_features:
                    values = df[feature]
                    f.write(f"  {feature}: {values.mean():.4f} ± {values.std():.4f}\n")
                f.write("\n")

            # 处理时间统计
            if 'Processing_Time' in df.columns:
                processing_times = df['Processing_Time']
                f.write("处理时间统计:\n")
                f.write(f"  平均处理时间: {processing_times.mean():.2f}秒\n")
                f.write(f"  最快处理时间: {processing_times.min():.2f}秒\n")
                f.write(f"  最慢处理时间: {processing_times.max():.2f}秒\n")
                f.write(f"  总处理时间: {processing_times.sum():.2f}秒\n\n")

        print(f"✅ 分析报告已保存: {report_file}")

    except Exception as e:
        print(f"❌ 生成报告失败: {e}")


def main():
    """主函数 - 一体化TDA分析流程"""
    print("开始HCC肿瘤TDA一体化分析...")
    overall_start_time = datetime.now()

    try:
        # 1. 批量分析
        result = batch_analyze_hcc()
        if result is None:
            print("❌ 批量分析失败!")
            return

        all_results, failed_files = result

        if not all_results:
            print("❌ 没有成功分析的文件!")
            return

        # 2. 保存到Excel
        excel_file, df = save_results_to_excel(all_results, OUTPUT_DIR)

        # 3. 生成可视化图表
        if SAVE_PLOTS:
            generate_visualizations(all_results, df, OUTPUT_DIR)

        # 4. 生成分析报告
        if GENERATE_REPORT:
            generate_analysis_report(df, OUTPUT_DIR)

        # 5. 显示总结信息
        total_time = (datetime.now() - overall_start_time).total_seconds()

        print(f"\n🎉 HCC肿瘤TDA一体化分析完成!")
        print("=" * 60)
        print(f"📊 分析结果:")
        print(f"  - 成功分析: {len(all_results)} 个文件")
        print(f"  - 失败文件: {len(failed_files)} 个")
        print(f"  - 提取特征: {len(df.columns)} 个")
        print(f"  - 总耗时: {total_time:.2f}秒")
        print(f"  - 平均每文件: {total_time/len(all_results):.2f}秒")

        print(f"\n📁 输出文件:")
        print(f"  - 输出目录: {OUTPUT_DIR}")
        if excel_file:
            print(f"  - Excel文件: {os.path.basename(excel_file)}")
        if SAVE_PLOTS:
            print(f"  - 可视化图表: *.png")
        if GENERATE_REPORT:
            print(f"  - 分析报告: HCC_TDA_Analysis_Report.txt")

        print(f"\n📈 关键统计信息:")
        if 'Tumor_Ratio' in df.columns:
            print(f"  - 平均肿瘤比例: {df['Tumor_Ratio'].mean():.4f}")
            print(f"  - 肿瘤比例范围: [{df['Tumor_Ratio'].min():.4f}, {df['Tumor_Ratio'].max():.4f}]")

        if 'Processing_Time' in df.columns:
            print(f"  - 平均处理时间: {df['Processing_Time'].mean():.2f}秒")

        # 显示主要拓扑特征
        entropy_cols = [col for col in df.columns if 'PC_Entropy' in col]
        if entropy_cols:
            print(f"  - 点云持续熵均值: {[f'{df[col].mean():.3f}' for col in entropy_cols[:3]]}")

        print("=" * 60)
        print("分析完成! 请查看输出目录中的结果文件。")

    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()


# ==================== 程序入口 ====================
if __name__ == "__main__":
    print("HCC肿瘤3D图像TDA一体化分析工具")
    print("=" * 60)
    print("配置信息:")
    print(f"  图像路径: {IMAGE_DIR}")
    print(f"  Mask路径: {MASK_DIR}")
    print(f"  输出目录: {OUTPUT_DIR}")
    print(f"  最大边长: {MAX_EDGE_LENGTH}")
    print(f"  最大点云: {MAX_POINTS}")
    print(f"  Mask阈值: {MASK_THRESHOLD}")
    print(f"  保存图表: {SAVE_PLOTS}")
    print(f"  生成报告: {GENERATE_REPORT}")
    print("=" * 60)

    # 检查路径是否存在
    if not os.path.exists(IMAGE_DIR):
        print(f"❌ 图像目录不存在: {IMAGE_DIR}")
        print("请修改脚本顶部的 IMAGE_DIR 路径")
        exit(1)

    if not os.path.exists(MASK_DIR):
        print(f"❌ Mask目录不存在: {MASK_DIR}")
        print("请修改脚本顶部的 MASK_DIR 路径")
        exit(1)

    # 开始分析
    main()
