# HCC肿瘤TDA一体化分析工具使用说明

## 🎉 工具特点

我已经为您创建了一个**集成所有功能的单一Python文件**，包含：

✅ **特征提取** - 批量提取肿瘤区域的拓扑特征  
✅ **Excel保存** - 自动保存到结构化Excel文件  
✅ **可视化分析** - 生成多种分析图表  
✅ **分析报告** - 自动生成详细的文本报告  
✅ **一键运行** - 无需命令行参数，直接运行即可  

## 📁 文件说明

### 主要文件
- **`HCC肿瘤TDA一体化分析工具.py`** - 集成所有功能的单一文件

### 配置简单
所有配置都在文件顶部，只需修改路径即可：

```python
# 数据路径配置
IMAGE_DIR = r"H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap"
MASK_DIR = r"H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap"

# 分析参数配置
MAX_EDGE_LENGTH = 5.0      # Vietoris-Rips复形的最大边长
MAX_POINTS = 8000          # 最大点云大小
MASK_THRESHOLD = 0.5       # Mask二值化阈值

# 输出配置
OUTPUT_DIR = "HCC_TDA_Results"  # 输出目录
SAVE_PLOTS = True          # 是否保存可视化图片
GENERATE_REPORT = True     # 是否生成分析报告
```

## 🚀 使用方法

### 超级简单的使用方式

1. **修改路径**（如果需要）
   - 打开 `HCC肿瘤TDA一体化分析工具.py`
   - 修改顶部的 `IMAGE_DIR` 和 `MASK_DIR` 路径
   - 其他参数可以保持默认

2. **直接运行**
   ```bash
   python HCC肿瘤TDA一体化分析工具.py
   ```

3. **等待完成**
   - 程序会自动完成所有步骤
   - 无需任何用户输入
   - 所有结果保存在 `HCC_TDA_Results` 目录

## 📊 输出内容

### 自动生成的文件

1. **Excel文件** - `HCC_TDA_Features_YYYYMMDD_HHMMSS.xlsx`
   - HCC_TDA_Features: 主要拓扑特征数据
   - Feature_Descriptions: 特征说明
   - Statistics: 统计信息

2. **可视化图表** (PNG格式)
   - `basic_features_distribution.png` - 基本特征分布图
   - `correlation_matrix.png` - 拓扑特征相关性矩阵
   - `pca_analysis.png` - PCA主成分分析图
   - `tumor_statistics.png` - 肿瘤统计图
   - `topological_features_comparison.png` - 拓扑特征比较图

   **🆕 TDA专用可视化图表**:
   - `persistence_diagrams_[样本名].png` - 持续图可视化
   - `betti_curves_[样本名].png` - Betti曲线详细分析
   - `point_cloud_[样本名].png` - 3D点云可视化
   - `heat_persistence_images_[样本名].png` - 热核与持续图像
   - `multi_sample_betti_comparison.png` - 多样本Betti曲线比较

3. **分析报告** - `HCC_TDA_Analysis_Report.txt`
   - 肿瘤区域统计
   - 拓扑特征统计
   - 处理时间统计

## 🔧 核心功能

### 1. 自动文件匹配
- 自动匹配 `patient-ap.nii.gz` 和 `patient-ap-mask.nii.gz`
- 显示匹配状态和未匹配文件

### 2. 肿瘤区域提取
- 根据mask精确提取肿瘤区域
- 自动处理图像和mask尺寸不一致问题
- 统计肿瘤体素数量和比例

### 3. 拓扑特征分析
- **点云方法**: Vietoris-Rips复形
- **立方方法**: Cubical复形
- **特征类型**: 持续熵、振幅、Betti曲线、点数统计

### 4. 自动可视化
- 特征分布图
- 相关性热图
- PCA降维分析
- 肿瘤统计图表
- 拓扑特征比较

### 5. 智能报告
- 自动生成详细的分析报告
- 包含统计信息和关键发现
- 便于后续分析和论文写作

## 📈 预期运行效果

```
HCC肿瘤3D图像TDA一体化分析工具
============================================================
配置信息:
  图像路径: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap
  Mask路径: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap
  输出目录: HCC_TDA_Results
  最大边长: 5.0
  最大点云: 8000
  Mask阈值: 0.5
  保存图表: True
  生成报告: True
============================================================

开始HCC肿瘤TDA一体化分析...

开始批量分析...
搜索图像文件: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\image\ap
搜索mask文件: H:\1.HCC-dataset\850HCC\all-HCC\10HCC-taicang\mask\ap
找到 10 个图像文件
找到 10 个mask文件

✅ 匹配: baoyongxing-ap.nii.gz -> baoyongxing-ap-mask.nii.gz
✅ 匹配: chencanzhen-ap.nii.gz -> chencanzhen-ap-mask.nii.gz
...

匹配结果: 成功匹配 10 对文件

进度: 1/10
开始分析: baoyongxing-ap.nii.gz
✅ 分析完成: baoyongxing-ap.nii.gz (25.67秒)

进度: 2/10
开始分析: chencanzhen-ap.nii.gz
✅ 分析完成: chencanzhen-ap.nii.gz (23.45秒)
...

批量分析完成!
  - 成功: 10 个文件
  - 失败: 0 个文件

保存结果到Excel...
✅ Excel文件已保存: HCC_TDA_Results\HCC_TDA_Features_20241127_153022.xlsx

生成可视化图表...
✅ 可视化图表已保存到: HCC_TDA_Results

生成分析报告...
✅ 分析报告已保存: HCC_TDA_Results\HCC_TDA_Analysis_Report.txt

🎉 HCC肿瘤TDA一体化分析完成!
============================================================
📊 分析结果:
  - 成功分析: 10 个文件
  - 失败文件: 0 个
  - 提取特征: 45 个
  - 总耗时: 245.67秒
  - 平均每文件: 24.57秒

📁 输出文件:
  - 输出目录: HCC_TDA_Results
  - Excel文件: HCC_TDA_Features_20241127_153022.xlsx
  - 可视化图表: *.png
  - 分析报告: HCC_TDA_Analysis_Report.txt

📈 关键统计信息:
  - 平均肿瘤比例: 0.0234
  - 肿瘤比例范围: [0.0045, 0.0892]
  - 平均处理时间: 24.57秒
  - 点云持续熵均值: ['12.345', '8.234', '5.123']
============================================================
分析完成! 请查看输出目录中的结果文件。
```

## 🎯 主要优势

### 1. 极简使用
- **一个文件包含所有功能**
- **无需命令行参数**
- **自动化程度极高**

### 2. 功能完整
- **特征提取** ✅
- **Excel保存** ✅  
- **可视化分析** ✅
- **报告生成** ✅

### 3. 智能处理
- **自动文件匹配**
- **错误处理完善**
- **进度显示清晰**

### 4. 结果丰富
- **45+个拓扑特征**
- **5种可视化图表**
- **详细分析报告**

## ⚙️ 自定义配置

如果需要调整参数，只需修改文件顶部的配置：

```python
# 如果图像较大，可以减少点云大小
MAX_POINTS = 6000

# 如果计算时间太长，可以减少边长
MAX_EDGE_LENGTH = 4.0

# 如果不需要可视化，可以关闭
SAVE_PLOTS = False

# 如果不需要报告，可以关闭
GENERATE_REPORT = False
```

## 🔍 故障排除

### 常见问题

1. **路径不存在**
   - 检查 `IMAGE_DIR` 和 `MASK_DIR` 路径是否正确
   - 确保路径中包含 .nii.gz 文件

2. **文件匹配失败**
   - 确保image文件名为 `patient-ap.nii.gz` 格式
   - 确保mask文件名为 `patient-ap-mask.nii.gz` 格式

3. **内存不足**
   - 减少 `MAX_POINTS` 参数
   - 减少 `MAX_EDGE_LENGTH` 参数

4. **计算时间过长**
   - 减少 `MAX_POINTS` 和 `MAX_EDGE_LENGTH`
   - 设置 `SAVE_PLOTS = False` 跳过可视化

## 🎉 总结

这个一体化工具完美解决了您的需求：

✅ **单一文件** - 不再需要多个py文件  
✅ **无参数运行** - 直接双击或python运行  
✅ **功能完整** - 特征提取+可视化+报告一应俱全  
✅ **自动化** - 从文件匹配到结果保存全自动  
✅ **专业输出** - Excel+图表+报告，适合科研使用  

现在您只需要运行一个命令就能完成所有的HCC肿瘤TDA分析工作！

---

**文件**: `HCC肿瘤TDA一体化分析工具.py`  
**使用**: `python HCC肿瘤TDA一体化分析工具.py`  
**输出**: `HCC_TDA_Results/` 目录中的所有结果文件
